package sitereport

// GetListReq represents the request parameters for getting site report list
type GetListReq struct {
	StartDate string `query:"start_date"`
	EndDate   string `query:"end_date"`
}

// GetListResp represents the response structure for site report list
type GetListResp struct {
	Year  string      `json:"year"`
	Month []MonthData `json:"month"`
}

type MonthData struct {
	Value       string     `json:"value"`
	Worker      int64      `json:"worker"`
	TotalAmount float64    `json:"total_amount"`
	Date        []DateData `json:"date"`
}

type DateData struct {
	Value       string       `json:"value"`
	Worker      int64        `json:"worker"`
	TotalAmount float64      `json:"total_amount"`
	Report      []ReportData `json:"report"`
}

type ReportData struct {
	SiteReportID int64   `json:"site_report_id"`
	SiteName     string  `json:"site_name"`
	Worker       int64   `json:"worker"`
	TotalAmount  float64 `json:"total_amount"`
	BStartTime   string  `json:"b_start_time"`
	BEndTime     string  `json:"b_end_time"`
	Note         string  `json:"note"`
	IsLocked     bool    `json:"is_locked"`
}

// BulkUpdateReq represents the request structure for bulk updating site reports
type BulkUpdateReq struct {
	SiteReportIDs   []int64 `json:"site_report_ids"`
	WorkDate        *string `json:"work_date,omitempty"`         // Optional, format: YYYY-MM-DD
	IsLocked        *bool   `json:"is_locked,omitempty"`         // Optional boolean
	IsInvoiceIssued *bool   `json:"is_invoice_issued,omitempty"` // Optional boolean

	UserRoles []string
}

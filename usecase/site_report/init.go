package sitereport

import (
	sitereportDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/site_report"
)

type SiteReportUseCase struct {
	sitereport sitereportDmn.SiteReportDomainItf
}

type Domains struct {
	SiteReportDomain sitereportDmn.SiteReportDomainItf
}

func InitSiteReportUseCase(d Domains) *SiteReportUseCase {
	uc := &SiteReportUseCase{
		sitereport: d.SiteReportDomain,
	}
	return uc
}

package customer

import (
	customerDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/customer"
	departmentDmn "github.com/Sera-Global/be-nbs-accounting-system/domain/department"
)

type CustomerUseCase struct {
	customer   customerDmn.CustomerDomainItf
	department departmentDmn.DepartmentDomainItf
}

type Domains struct {
	CustomerDomain   customerDmn.CustomerDomainItf
	DepartmentDomain departmentDmn.DepartmentDomainItf
}

func InitCustomerUseCase(d Domains) *CustomerUseCase {
	uc := &CustomerUseCase{
		customer:   d.CustomerDomain,
		department: d.DepartmentDomain,
	}
	return uc
}

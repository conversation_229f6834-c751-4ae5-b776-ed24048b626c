package customer

import "github.com/Sera-Global/be-nbs-accounting-system/common/types"

// GetListReq represents the request parameters for getting customer list
type GetListReq struct {
	types.BasicGetParam
}

// GetListResp represents the response structure for customer list
type GetListResp struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

// GetDepartmentListReq represents the request parameters for getting department list
type GetDepartmentListReq struct {
	CustomerID int64 `query:"customer_id"`
	types.BasicGetParam
}

// GetDepartmentListResp represents the response structure for department list
type GetDepartmentListResp struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

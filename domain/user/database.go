package user

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getByUsername fetches a user by username.
func (rsc UserResource) getByUsername(ctx context.Context, username string) (User, error) {
	var user User

	db := dbmanager.Manager().WithContext(ctx)
	err := db.Where("username = ?", username).
		Where("deleted_at IS NULL").
		First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return User{}, nil
		}
		return User{}, log.LogError(err, nil)
	}

	return user, nil
}

// getLoginUser fetches a user by username and password.
func (rsc UserResource) getLoginUser(ctx context.Context, param User) error {
	db := dbmanager.Manager().WithContext(ctx)
	user := User{}

	if param.Username != "" {
		db = db.Where("username = ?", param.Username)
	}

	err := db.First(&user).Error
	if err != nil {
		return log.LogError(err, nil)
	}

	return nil
}

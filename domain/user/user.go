package user

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	UserDomainItf interface {
		GetByUsername(ctx context.Context, username string) (User, error)
		GetLoginUser(ctx context.Context, param User) error
	}

	UserResourceItf interface {
		getByUsername(ctx context.Context, username string) (User, error)
		getLoginUser(ctx context.Context, param User) error
	}
)

// GetByUsername retrieves user by username.
func (d *UserDomain) GetByUsername(ctx context.Context, username string) (User, error) {
	user, err := d.resource.getByUsername(ctx, username)
	if err != nil {
		return User{}, log.LogError(err, nil)
	}
	return user, nil
}

// Get<PERSON><PERSON>inUser retrieves user by username and password.
func (d *UserDomain) GetLoginUser(ctx context.Context, param User) error {
	err := d.resource.getLoginUser(ctx, param)
	if err != nil {
		return log.LogError(err, nil)
	}
	return nil
}

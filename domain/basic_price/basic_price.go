package basicprice

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	BasicPriceDomainItf interface {
		GetList(ctx context.Context, param GetListReq) ([]BasicPrice, error)
	}

	BasicPriceResourceItf interface {
		getList(ctx context.Context, param GetListReq) ([]BasicPrice, error)
	}
)

// GetList retrieves all basic prices ordered by title.
func (d *BasicPriceDomain) GetList(ctx context.Context, param GetListReq) ([]BasicPrice, error) {
	basicPrices, err := d.resource.getList(ctx, param)
	if err != nil {
		return []BasicPrice{}, log.LogError(err, nil)
	}
	return basicPrices, nil
}

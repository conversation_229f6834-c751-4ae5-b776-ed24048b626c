package customer

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	customerusecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/customer"
	"github.com/labstack/echo/v4"
)

func TestValidateGetDepartmentList(t *testing.T) {
	tests := []struct {
		name    string
		req     customerusecase.GetDepartmentListReq
		wantErr bool
	}{
		{
			name: "Valid request",
			req: customerusecase.GetDepartmentListReq{
				CustomerID: 1,
			},
			wantErr: false,
		},
		{
			name: "Missing customer_id (zero value)",
			req: customerusecase.GetDepartmentListReq{
				CustomerID: 0,
			},
			wantErr: true,
		},
		{
			name: "Negative customer_id",
			req: customerusecase.GetDepartmentListReq{
				CustomerID: -1,
			},
			wantErr: true,
		},
		{
			name: "Valid large customer_id",
			req: customerusecase.GetDepartmentListReq{
				CustomerID: 999999,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateGetDepartmentList(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateGetDepartmentList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGetDepartmentListRequestBinding(t *testing.T) {
	e := echo.New()

	tests := []struct {
		name       string
		customerID string
		expectBind bool
	}{
		{
			name:       "Valid customer_id",
			customerID: "123",
			expectBind: true,
		},
		{
			name:       "Missing customer_id parameter",
			customerID: "",
			expectBind: true, // Binding succeeds but validation should fail
		},
		{
			name:       "Invalid customer_id (non-numeric)",
			customerID: "abc",
			expectBind: false, // Binding should fail for non-numeric
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create request URL with query parameter
			reqURL := "/customer/department/list"
			if tt.customerID != "" {
				reqURL += "?customer_id=" + url.QueryEscape(tt.customerID)
			}

			req := httptest.NewRequest(http.MethodGet, reqURL, nil)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Test just the binding part
			var reqStruct customerusecase.GetDepartmentListReq
			err := c.Bind(&reqStruct)

			if tt.expectBind && err != nil {
				t.Errorf("Expected successful binding but got error: %v", err)
			}
			if !tt.expectBind && err == nil {
				t.Errorf("Expected binding error but got none")
			}

			// If binding succeeded, test validation
			if err == nil {
				validationErr := validateGetDepartmentList(reqStruct)
				// For empty customer_id, validation should fail
				if tt.customerID == "" && validationErr == nil {
					t.Errorf("Expected validation error for empty customer_id")
				}
				// For valid customer_id, validation should pass
				if tt.customerID == "123" && validationErr != nil {
					t.Errorf("Expected validation to pass for valid customer_id, got: %v", validationErr)
				}
			}
		})
	}
}

// TestDepartmentListResponseStructure verifies the expected response format
func TestDepartmentListResponseStructure(t *testing.T) {
	// Test that the response types match the expected JSON structure
	resp := customerusecase.GetDepartmentListResp{
		ID:   1,
		Name: "Finance",
	}

	// Verify the JSON tags are correct
	expectedJSON := `{"id":1,"name":"Finance"}`
	actualJSON, err := json.Marshal(resp)
	if err != nil {
		t.Errorf("Failed to marshal response: %v", err)
	}

	if string(actualJSON) != expectedJSON {
		t.Errorf("Expected JSON %s, got %s", expectedJSON, string(actualJSON))
	}

	// Test array response structure
	respArray := []customerusecase.GetDepartmentListResp{
		{ID: 1, Name: "Finance"},
		{ID: 2, Name: "IT"},
	}

	expectedArrayJSON := `[{"id":1,"name":"Finance"},{"id":2,"name":"IT"}]`
	actualArrayJSON, err := json.Marshal(respArray)
	if err != nil {
		t.Errorf("Failed to marshal array response: %v", err)
	}

	if string(actualArrayJSON) != expectedArrayJSON {
		t.Errorf("Expected array JSON %s, got %s", expectedArrayJSON, string(actualArrayJSON))
	}
}

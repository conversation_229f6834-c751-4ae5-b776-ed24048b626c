package customer

import (
	"net/http"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/server"
	customerUsecase "github.com/Sera-Global/be-nbs-accounting-system/usecase/customer"
	"github.com/labstack/echo/v4"
)

func GetList(c echo.Context) error {
	var status int

	var req customerUsecase.GetListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.CustomerUseCase.GetList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[customer.GetList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

func GetDepartmentList(c echo.Context) error {
	var status int

	var req customerUsecase.GetDepartmentListReq
	err := c.Bind(&req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	err = validateGetDepartmentList(req)
	if err != nil {
		status = http.StatusBadRequest
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	respData, err := server.CustomerUseCase.GetDepartmentList(c.Request().Context(), req)
	if err != nil {
		status = http.StatusInternalServerError
		return c.JSON(status, types.BasicResp{Message: err.Error()})
	}

	resp := types.BasicResp{
		Message: constanta.Success,
		Data:    respData,
	}
	status = http.StatusOK
	log.LogInfo("[customer.GetDepartmentList]", map[string]interface{}{"resp:": resp})

	return c.JSON(status, resp)
}

package site_report

import (
	"testing"

	constanta "github.com/Sera-Global/be-nbs-accounting-system/common/const"
	sitereport "github.com/Sera-Global/be-nbs-accounting-system/usecase/site_report"
)

func TestValidateGetList(t *testing.T) {
	tests := []struct {
		name    string
		req     sitereport.GetListReq
		wantErr bool
	}{
		{
			name: "Valid request",
			req: sitereport.GetListReq{
				StartDate: "2025-05-22",
				EndDate:   "2025-07-22",
			},
			wantErr: false,
		},
		{
			name: "Missing start date",
			req: sitereport.GetListReq{
				StartDate: "",
				EndDate:   "2025-07-22",
			},
			wantErr: true,
		},
		{
			name: "Missing end date",
			req: sitereport.GetListReq{
				StartDate: "2025-05-22",
				EndDate:   "",
			},
			wantErr: true,
		},
		{
			name: "Invalid start date format",
			req: sitereport.GetListReq{
				StartDate: "2025/05/22",
				EndDate:   "2025-07-22",
			},
			wantErr: true,
		},
		{
			name: "Invalid end date format",
			req: sitereport.GetListReq{
				StartDate: "2025-05-22",
				EndDate:   "2025/07/22",
			},
			wantErr: true,
		},
		{
			name: "Invalid date values",
			req: sitereport.GetListReq{
				StartDate: "2025-13-01",
				EndDate:   "2025-07-22",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateGetList(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateGetList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestValidateBulkUpdate(t *testing.T) {
	// Define some helper variables
	validDate := "2025-07-22"
	invalidDate := "2025/07/22"
	isLockedTrue := true
	isLockedFalse := false
	isInvoiceIssuedTrue := true

	tests := []struct {
		name    string
		req     sitereport.BulkUpdateReq
		wantErr bool
	}{
		// 1. Valid: SuperAdmin updating work_date (and other fields)
		{
			name: "SuperAdmin updates all fields",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs:   []int64{1, 2, 3},
				WorkDate:        &validDate,
				IsLocked:        &isLockedTrue,
				IsInvoiceIssued: &isInvoiceIssuedTrue,
				UserRoles:       []string{constanta.RoleSuperAdmin},
			},
			wantErr: false,
		},
		// 2. Invalid: Admin tries to update work_date
		{
			name: "Admin updates work_date (unauthorized)",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{1, 2, 3},
				WorkDate:      &validDate,
				UserRoles:     []string{constanta.RoleSupervisor},
			},
			wantErr: true,
		},
		// 3. Valid: Admin locks site reports (is_locked = true)
		{
			name: "Admin locks site reports",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{1, 2, 3},
				IsLocked:      &isLockedTrue,
				UserRoles:     []string{constanta.RoleAdmin},
			},
			wantErr: false,
		},
		// 4. Invalid: SubAdmin tries to lock site reports (unauthorized)
		{
			name: "SubAdmin locks site reports (unauthorized)",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{1, 2, 3},
				IsLocked:      &isLockedTrue,
				UserRoles:     []string{constanta.RoleSubAdmin},
			},
			wantErr: true,
		},
		// 5. Valid: SuperAdmin unlocks site reports (is_locked = false)
		{
			name: "SuperAdmin unlocks site reports",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{1, 2, 3},
				IsLocked:      &isLockedFalse,
				UserRoles:     []string{constanta.RoleSuperAdmin},
			},
			wantErr: false,
		},
		// 6. Invalid: Admin unlocks site reports (unauthorized)
		{
			name: "Admin unlocks site reports (unauthorized)",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{1, 2, 3},
				IsLocked:      &isLockedFalse,
				UserRoles:     []string{constanta.RoleAdmin},
			},
			wantErr: true,
		},
		// 7. Valid: SubAdmin marks invoice issued
		{
			name: "SubAdmin marks invoice issued",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs:   []int64{1, 2, 3},
				IsInvoiceIssued: &isInvoiceIssuedTrue,
				UserRoles:       []string{constanta.RoleSubAdmin},
			},
			wantErr: false,
		},
		// 8. Invalid: Unauthorized role marks invoice issued
		{
			name: "Unauthorized role marks invoice issued",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs:   []int64{1, 2, 3},
				IsInvoiceIssued: &isInvoiceIssuedTrue,
				UserRoles:       []string{"wrong role"},
			},
			wantErr: true,
		},
		// 9. Empty site_report_ids (validation failure prior to role checks)
		{
			name: "Empty site_report_ids",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{},
				WorkDate:      &validDate,
				UserRoles:     []string{constanta.RoleSuperAdmin},
			},
			wantErr: true,
		},
		// 10. No update fields provided
		{
			name: "No fields provided",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{1, 2, 3},
				UserRoles:     []string{constanta.RoleSuperAdmin},
			},
			wantErr: true,
		},
		// 11. Invalid date format
		{
			name: "Invalid date format",
			req: sitereport.BulkUpdateReq{
				SiteReportIDs: []int64{1, 2, 3},
				WorkDate:      &invalidDate,
				UserRoles:     []string{constanta.RoleSuperAdmin},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateBulkUpdate(tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("validateBulkUpdate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
